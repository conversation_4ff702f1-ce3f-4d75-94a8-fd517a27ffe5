﻿  main.cpp
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(162,36): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(162,36): warning C4244:         with
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(162,36): warning C4244:         [
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(162,36): warning C4244:             _Ty=__int64
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(162,36): warning C4244:         ]
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(175,34): warning C4244: “初始化”: 从“__int64”转换到“int”，可能丢失数据
C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(210,30): warning C4244: “初始化”: 从“__int64”转换到“int”，可能丢失数据
D:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple(132,47): warning C4244: “初始化”: 从“__int64”转换到“_Ty”，可能丢失数据
D:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple(132,47): warning C4244:         with
D:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple(132,47): warning C4244:         [
D:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple(132,47): warning C4244:             _Ty=int
D:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple(132,47): warning C4244:         ]
  (编译源文件“../solution/main.cpp”)
      D:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple(132,47):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(224,27):
          查看对正在编译的函数 模板 实例化“void std::vector<std::tuple<int,int,int,int>,std::allocator<std::tuple<int,int,int,int>>>::emplace_back<__int64&,int,int&,int&>(__int64 &,int &&,int &,int &)”的引用
              C:\Users\<USER>\Desktop\华为比赛\solution\main.cpp(224,39):
              请参阅 "main" 中对 "std::vector<std::tuple<int,int,int,int>,std::allocator<std::tuple<int,int,int,int>>>::emplace_back" 的第一个引用
          D:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(924,24):
          查看对正在编译的函数 模板 实例化“_Ty &std::vector<_Ty,std::allocator<_Ty>>::_Emplace_one_at_back<__int64&,int,int&,int&>(__int64 &,int &&,int &,int &)”的引用
          with
          [
              _Ty=std::tuple<int,int,int,int>
          ]
          D:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(845,20):
          查看对正在编译的函数 模板 实例化“_Ty &std::vector<_Ty,std::allocator<_Ty>>::_Emplace_back_with_unused_capacity<__int64&,int,int&,int&>(__int64 &,int &&,int &,int &)”的引用
          with
          [
              _Ty=std::tuple<int,int,int,int>
          ]
          D:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(860,18):
          查看对正在编译的函数 模板 实例化“void std::_Construct_in_place<std::tuple<int,int,int,int>,__int64&,_Ty,int&,int&>(std::tuple<int,int,int,int> &,__int64 &,_Ty &&,int &,int &) noexcept”的引用
          with
          [
              _Ty=int
          ]
          D:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(476,61):
          查看对正在编译的函数 模板 实例化“std::tuple<int,int,int,int>::tuple<__int64&,_T,int&,int&,0>(_This2,_T &&,int &,int &) noexcept”的引用
          with
          [
              _T=int,
              _This2=__int64 &
          ]
          D:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple(349,16):
          查看对正在编译的函数 模板 实例化“std::tuple<int,int,int,int>::tuple<std::_Exact_args_t,__int64&,_Ty,int&,int&,0>(_Tag,_This2,_Ty &&,int &,int &)”的引用
          with
          [
              _Ty=int,
              _Tag=std::_Exact_args_t,
              _This2=__int64 &
          ]
          D:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple(301,102):
          查看对正在编译的函数 模板 实例化“std::_Tuple_val<_This>::_Tuple_val<__int64&>(_Other)”的引用
          with
          [
              _This=int,
              _Other=__int64 &
          ]
  
  ConsoleApplication1.vcxproj -> C:\Users\<USER>\Desktop\华为比赛\ConsoleApplication1\x64\Debug\ConsoleApplication1.exe
